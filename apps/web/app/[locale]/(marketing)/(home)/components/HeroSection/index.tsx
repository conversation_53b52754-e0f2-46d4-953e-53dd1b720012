'use client'

import { useState, useCallback, useRef } from 'react'
import './hero-animations.css'
import { useRouter } from '@i18n/routing'
import { Upload, ArrowRight, Image as ImageIcon } from 'lucide-react'
import Image from 'next/image'
import { useImageTransfer } from '../../../../../../hooks/useImageTransfer'

const PRESET_BACKGROUNDS = [
  'https://images.pexels.com/photos/********/pexels-photo-********.jpeg?auto=compress&cs=tinysrgb&w=600&loading=lazy',
  'https://images.pexels.com/photos/********/pexels-photo-********.jpeg?auto=compress&cs=tinysrgb&w=600&loading=lazy',
  'https://images.pexels.com/photos/********/pexels-photo-********.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500',
  'https://images.pexels.com/photos/********/pexels-photo-********.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500',
]

export function HeroSection() {
  const router = useRouter()
  const { urlToTransferData, setImagesToTransfer, filesToTransferData } =
    useImageTransfer()
  const [isDragActive, setIsDragActive] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [loadingPresetIndex, setLoadingPresetIndex] = useState<number | null>(
    null
  )
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Global drag and drop handlers with simplified logic
  const handleGlobalDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()

    // Only handle file drags
    if (e.dataTransfer.types.includes('Files')) {
      setIsDragActive(true)
    }
  }, [])

  const handleGlobalDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
  }, [])

  const handleGlobalDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()

    // Check if we're actually leaving the container
    const relatedTarget = e.relatedTarget as Element
    const currentTarget = e.currentTarget as Element

    // If relatedTarget is null or not contained within currentTarget, we're leaving
    if (!relatedTarget || !currentTarget.contains(relatedTarget)) {
      setIsDragActive(false)
    }
  }, [])

  // Handle file upload (similar to ImageUpload component logic)
  const handleFiles = useCallback(
    async (files: File[]) => {
      if (files.length === 0) return

      // Validate file types
      const validFiles = files.filter(
        (file) =>
          file.type.startsWith('image/') &&
          ['image/jpeg', 'image/png', 'image/webp'].includes(file.type)
      )

      if (validFiles.length === 0) return

      // Validate file sizes (max 10MB each)
      const validSizeFiles = validFiles.filter(
        (file) => file.size <= 10 * 1024 * 1024
      )
      if (validSizeFiles.length === 0) return

      setIsUploading(true)

      try {
        // Convert files to transfer data (similar to ImageUpload progress simulation)
        const transferData = await filesToTransferData(validSizeFiles)

        // 设置shouldShowProgress标志，让ImageEditor显示进度条
        const transferDataWithProgress = transferData.map((data) => ({
          ...data,
          shouldShowProgress: true,
        }))

        setImagesToTransfer(transferDataWithProgress)

        // Navigate to playground
        router.push('/playground')
      } catch (error) {
        console.error('Failed to process files:', error)
      } finally {
        setIsUploading(false)
      }
    },
    [filesToTransferData, setImagesToTransfer, router]
  )

  const handleGlobalDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault()
      e.stopPropagation()
      setIsDragActive(false)

      // Only handle file drops
      if (e.dataTransfer.files.length > 0) {
        const files = Array.from(e.dataTransfer.files)
        handleFiles(files)
      }
    },
    [handleFiles]
  )

  const handleUploadClick = () => {
    // Trigger file input click
    fileInputRef.current?.click()
  }

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const files = Array.from(e.target.files)
      handleFiles(files)
    }
  }

  const handlePresetClick = useCallback(
    async (imageUrl: string, index: number) => {
      if (typeof loadingPresetIndex === 'number') {
        return
      }
      try {
        setLoadingPresetIndex(index)

        // Convert preset image to transfer data
        const transferData = await urlToTransferData(
          imageUrl,
          `preset-${index + 1}.jpg`
        )

        // 设置shouldShowProgress标志，让ImageEditor显示进度条
        const transferDataWithProgress = {
          ...transferData,
          shouldShowProgress: true,
        }

        setImagesToTransfer([transferDataWithProgress])

        // Navigate to playground
        router.push('/playground')
      } catch (error) {
        console.error('Failed to load preset image:', error)
      } finally {
        setTimeout(() => {
          setLoadingPresetIndex(null)
        }, 500)
      }
    },
    [urlToTransferData, setImagesToTransfer, router]
  )

  return (
    <>
      <div
        className="relative min-h-screen flex items-center justify-center px-4 py-16"
        onDragEnter={handleGlobalDragEnter}
        onDragOver={handleGlobalDragOver}
        onDragLeave={handleGlobalDragLeave}
        onDrop={handleGlobalDrop}
      >
        {/* Background decorative elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div
            className="absolute top-20 left-10 w-20 h-20 bg-yellow-200 rounded-full opacity-20 animate-pulse"
            style={{ animationDuration: '3s' }}
          ></div>
          <div
            className="absolute top-40 right-20 w-16 h-16 bg-blue-200 rounded-full opacity-30 animate-bounce"
            style={{ animationDuration: '2s' }}
          ></div>
          <div
            className="absolute bottom-32 left-1/4 w-12 h-12 bg-purple-200 rounded-full opacity-25 animate-pulse"
            style={{ animationDuration: '4s' }}
          ></div>
          <div
            className="absolute top-1/2 right-1/3 w-8 h-8 bg-pink-200 rounded-full opacity-20 animate-ping"
            style={{ animationDuration: '3s' }}
          ></div>
        </div>

        <div className="max-w-7xl mx-auto grid lg:grid-cols-2 gap-12 items-center relative z-10">
          {/* Left side - Content */}
          <div className="space-y-8 animate-fade-in-left">
            {/* Main heading with animation */}
            <div className="space-y-4">
              <h1 className="text-5xl lg:text-6xl font-bold text-gray-900 leading-tight animate-fade-in-up">
                Remove Image
                <br />
                <span className="bg-gradient-to-r from-yellow-400 via-orange-400 to-yellow-500 bg-clip-text text-transparent animate-gradient">
                  Background
                </span>
              </h1>

              <div className="flex items-center gap-2 text-lg text-gray-600 animate-fade-in-up animate-delay-200">
                <span className="font-medium">100% Automatically and</span>
                <span className="bg-yellow-400 text-yellow-900 px-3 py-1 rounded-full text-sm font-bold animate-float">
                  Free
                </span>
              </div>
            </div>

            {/* Upload button */}
            <div className="space-y-4 animate-fade-in-up animate-delay-300">
              <button
                onClick={handleUploadClick}
                disabled={isUploading}
                className={`group text-white px-8 py-4 rounded-2xl text-lg font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-lg flex items-center gap-3 hover-lift ${
                  isUploading
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-blue-500 hover:bg-blue-600'
                }`}
              >
                {isUploading ? (
                  <>
                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>
                    <Upload className="w-5 h-5" />
                    Upload Image
                  </>
                )}
              </button>

              <p className="text-sm text-gray-500">
                or drop a file,
                <br />
                paste image or URL
              </p>
            </div>
          </div>

          {/* Right side - Upload area and presets */}
          <div className="space-y-6 animate-fade-in-right animate-delay-400">
            {/* Upload area */}
            <div
              className="group bg-white rounded-3xl border-2 border-dashed border-gray-200 p-8 text-center hover:border-blue-300 hover:bg-blue-50/30 transition-all duration-300 min-h-[300px] flex flex-col items-center justify-center cursor-pointer"
              onClick={handleUploadClick}
            >
              <div className="space-y-4">
                <div className="w-16 h-16 bg-blue-100 group-hover:bg-blue-200 rounded-full flex items-center justify-center mx-auto transition-colors duration-300">
                  <ImageIcon className="w-8 h-8 text-blue-500 group-hover:text-blue-600 transition-colors duration-300" />
                </div>
                <div>
                  <p className="text-lg font-medium text-gray-700 group-hover:text-blue-700 mb-2 transition-colors duration-300">
                    Upload Image
                  </p>
                  <p className="text-sm text-gray-500 group-hover:text-blue-600 transition-colors duration-300">
                    or drop a file,
                    <br />
                    paste image or URL
                  </p>
                </div>
              </div>
            </div>

            {/* Preset images */}
            <div className="space-y-3">
              <p className="text-sm text-gray-600 font-medium">No image?</p>
              <p className="text-sm text-gray-500">Try one of these:</p>

              <div className="grid grid-cols-4 gap-3">
                {PRESET_BACKGROUNDS.map((imageUrl, index) => (
                  <button
                    key={index}
                    onClick={() => handlePresetClick(imageUrl, index)}
                    disabled={loadingPresetIndex === index}
                    className={`group relative aspect-square rounded-lg overflow-hidden border-2 transition-all duration-300 ${
                      loadingPresetIndex === index
                        ? 'border-blue-500 cursor-not-allowed'
                        : 'border-transparent hover:border-blue-400 transform hover:scale-105'
                    }`}
                  >
                    <Image
                      src={imageUrl}
                      alt={`Preset ${index + 1}`}
                      fill
                      className={`object-cover transition-all duration-300 ${
                        loadingPresetIndex === index ? 'opacity-50' : ''
                      }`}
                      sizes="(max-width: 768px) 25vw, 12vw"
                    />

                    {/* Loading overlay */}
                    {loadingPresetIndex === index ? (
                      <div className="absolute inset-0 bg-blue-500 bg-opacity-20 flex items-center justify-center">
                        <div className="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      </div>
                    ) : (
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
                        <ArrowRight className="w-5 h-5 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                      </div>
                    )}
                  </button>
                ))}
              </div>
            </div>

            {/* Terms notice */}
            <div className="text-xs text-gray-400 space-y-1">
              <p>
                By uploading an image or URL you agree to our{' '}
                <a href="/terms" className="text-blue-500 hover:underline">
                  Terms of Service
                </a>
                . To learn more about how
              </p>
              <p>
                remove.bg handles your personal data, check our{' '}
                <a href="/privacy" className="text-blue-500 hover:underline">
                  Privacy Policy
                </a>
                .
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Drag overlay */}
      {isDragActive && (
        <div className="fixed inset-0 z-50 bg-black/20 backdrop-blur-sm flex items-center justify-center p-4 pointer-events-none">
          <div className="bg-white/95 backdrop-blur-md rounded-3xl border-4 border-dashed border-blue-400 p-12 max-w-2xl w-full text-center shadow-2xl relative">
            {/* Corner decorations */}
            <div className="absolute top-4 left-4 w-8 h-8 border-l-4 border-t-4 border-blue-400 rounded-tl-lg"></div>
            <div className="absolute top-4 right-4 w-8 h-8 border-r-4 border-t-4 border-blue-400 rounded-tr-lg"></div>
            <div className="absolute bottom-4 left-4 w-8 h-8 border-l-4 border-b-4 border-blue-400 rounded-bl-lg"></div>
            <div className="absolute bottom-4 right-4 w-8 h-8 border-r-4 border-b-4 border-blue-400 rounded-br-lg"></div>

            {/* Upload icon */}
            <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <Upload className="w-10 h-10 text-blue-500" />
            </div>

            <div className="text-4xl font-bold text-gray-800 mb-4">
              Drop image here
            </div>
            <div className="text-lg text-gray-600 mb-8">
              Release to upload and start editing
            </div>

            {/* File type indicators */}
            <div className="flex justify-center space-x-4">
              <div className="bg-blue-100 p-3 rounded-lg border border-blue-200">
                <div className="w-8 h-8 bg-blue-500 rounded-sm flex items-center justify-center">
                  <div className="text-white text-xs font-bold">JPG</div>
                </div>
              </div>
              <div className="bg-green-100 p-3 rounded-lg border border-green-200">
                <div className="w-8 h-8 bg-green-500 rounded-sm flex items-center justify-center">
                  <div className="text-white text-xs font-bold">PNG</div>
                </div>
              </div>
              <div className="bg-purple-100 p-3 rounded-lg border border-purple-200">
                <div className="w-8 h-8 bg-purple-500 rounded-sm flex items-center justify-center">
                  <div className="text-white text-xs font-bold">WEBP</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/jpeg,image/png,image/webp"
        multiple
        onChange={handleFileInputChange}
        className="hidden"
      />
    </>
  )
}
