import { NextRequest, NextResponse } from 'next/server'

// 从环境变量获取Hugging Face API URL
const HUGGING_FACE_BASE_URL = process.env.NEXT_PUBLIC_HUGGING_FACE_BASE_URL || 
  'https://faith1314666-imggen-magic-wand.hf.space'

// RemoveBG插件默认配置（用于获取前景对象）
const DEFAULT_REMOVE_BG_CONFIG = {
  name: 'RemoveBG',
  clicks: [],
  scale: 1.0,
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      image,
      blurIntensity = 20,
      model = 'u2net',
      ...otherConfig 
    } = body

    // 验证必需参数
    if (!image) {
      return NextResponse.json(
        { error: 'Image is required' },
        { status: 400 }
      )
    }

    // 首先调用背景移除API获取前景对象
    const removeBgRequestBody = {
      ...DEFAULT_REMOVE_BG_CONFIG,
      image,
      ...otherConfig,
    }

    const removeBgResponse = await fetch(`${HUGGING_FACE_BASE_URL}/api/v1/run_plugin_gen_image`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'image/*',
      },
      body: JSON.stringify(removeBgRequestBody),
    })

    if (!removeBgResponse.ok) {
      const errorText = await removeBgResponse.text()
      console.error('Background removal API error:', {
        status: removeBgResponse.status,
        statusText: removeBgResponse.statusText,
        error: errorText,
      })
      
      return NextResponse.json(
        { 
          error: `Background removal failed: ${removeBgResponse.status} ${removeBgResponse.statusText}`,
          details: errorText 
        },
        { status: removeBgResponse.status }
      )
    }

    // 获取移除背景后的图片
    let removedBgImageBase64: string

    const contentType = removeBgResponse.headers.get('content-type')
    if (contentType && contentType.startsWith('image/')) {
      // 如果返回的是图片blob，转换为base64
      const imageBlob = await removeBgResponse.blob()
      const arrayBuffer = await imageBlob.arrayBuffer()
      const buffer = Buffer.from(arrayBuffer)
      removedBgImageBase64 = buffer.toString('base64')
    } else {
      // 如果返回的是文本/JSON，尝试解析
      const result = await removeBgResponse.text()
      try {
        const jsonResult = JSON.parse(result)
        removedBgImageBase64 = jsonResult.image || result
      } catch {
        removedBgImageBase64 = result
      }
    }

    // 返回处理结果，包含原图和移除背景的图片
    // 客户端将使用这些数据进行前端合成和模糊处理
    return NextResponse.json({
      success: true,
      originalImage: image,
      removedBackgroundImage: removedBgImageBase64,
      blurIntensity,
      message: 'Background removal completed. Client-side blur composition will be applied.'
    })

  } catch (error) {
    console.error('Blur background API error:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
