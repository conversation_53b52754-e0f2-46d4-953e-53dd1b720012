import { NextRequest, NextResponse } from 'next/server'

// 从环境变量获取Hugging Face API URL
const HUGGING_FACE_BASE_URL = process.env.NEXT_PUBLIC_HUGGING_FACE_BASE_URL || 
  'https://faith1314666-imggen-magic-wand.hf.space'

// RemoveBG插件默认配置
const DEFAULT_REMOVE_BG_CONFIG = {
  name: 'RemoveBG',
  clicks: [],
  scale: 1.0,
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      image,
      model = 'u2net',
      ...otherConfig 
    } = body

    // 验证必需参数
    if (!image) {
      return NextResponse.json(
        { error: 'Image is required' },
        { status: 400 }
      )
    }

    // 构建请求体
    const requestBody = {
      ...DEFAULT_REMOVE_BG_CONFIG,
      image,
      ...otherConfig,
    }

    // 调用Hugging Face API
    const response = await fetch(`${HUGGING_FACE_BASE_URL}/api/v1/run_plugin_gen_image`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'image/*',
      },
      body: JSON.stringify(requestBody),
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('Background removal API error:', {
        status: response.status,
        statusText: response.statusText,
        error: errorText,
      })
      
      return NextResponse.json(
        { 
          error: `Background removal failed: ${response.status} ${response.statusText}`,
          details: errorText 
        },
        { status: response.status }
      )
    }

    // 检查响应内容类型
    const contentType = response.headers.get('content-type')
    
    if (contentType && contentType.startsWith('image/')) {
      // 如果返回的是图片，直接转发
      const imageBlob = await response.blob()
      const arrayBuffer = await imageBlob.arrayBuffer()
      const buffer = Buffer.from(arrayBuffer)
      
      return new NextResponse(buffer, {
        status: 200,
        headers: {
          'Content-Type': contentType,
          'Cache-Control': 'no-cache',
        },
      })
    } else {
      // 如果返回的是JSON（可能包含base64），解析并返回
      const result = await response.text()
      
      // 尝试解析为JSON
      try {
        const jsonResult = JSON.parse(result)
        return NextResponse.json(jsonResult)
      } catch {
        // 如果不是JSON，可能是base64字符串
        return NextResponse.json({ 
          success: true, 
          image: result,
          format: 'base64'
        })
      }
    }

  } catch (error) {
    console.error('Remove background API error:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
