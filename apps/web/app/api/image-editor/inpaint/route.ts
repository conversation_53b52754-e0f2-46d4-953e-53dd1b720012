import { NextRequest, NextResponse } from 'next/server'

// IOPaint API 默认配置
const DEFAULT_INPAINT_CONFIG = {
  ldm_steps: 20,
  ldm_sampler: 'plms',
  hd_strategy: 'Crop',
  hd_strategy_crop_trigger_size: 800,
  hd_strategy_crop_margin: 128,
  hd_strategy_resize_limit: 1280,
  prompt: '',
  negative_prompt: '',
  use_croper: false,
  croper_x: 0,
  croper_y: 0,
  use_extender: false,
  extender_x: 0,
  extender_y: 0,
  sd_mask_blur: 12,
  sd_strength: 1.0,
  sd_steps: 50,
  sd_guidance_scale: 7.5,
  sd_sampler: 'DPM++ 2M',
  sd_seed: -1,
  sd_match_histograms: false,
  sd_lcm_lora: false,
  enable_controlnet: false,
  controlnet_conditioning_scale: 0.4,
  controlnet_method: '',
  enable_brushnet: false,
  brushnet_method: '',
  brushnet_conditioning_scale: 1.0,
  enable_powerpaint_v2: false,
  powerpaint_task: 'object-remove',
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      image, 
      mask, 
      baseUrl, 
      width, 
      height,
      // 允许客户端覆盖部分配置
      prompt,
      negative_prompt,
      sd_steps,
      sd_guidance_scale,
      ...otherConfig 
    } = body

    // 验证必需参数
    if (!image || !mask) {
      return NextResponse.json(
        { error: 'Image and mask are required' },
        { status: 400 }
      )
    }

    if (!baseUrl) {
      return NextResponse.json(
        { error: 'IOPaint server URL is required' },
        { status: 400 }
      )
    }

    // 构建请求体，合并默认配置和客户端配置
    const requestBody = {
      ...DEFAULT_INPAINT_CONFIG,
      image,
      mask,
      croper_height: height || DEFAULT_INPAINT_CONFIG.croper_y,
      croper_width: width || DEFAULT_INPAINT_CONFIG.croper_x,
      extender_height: height || DEFAULT_INPAINT_CONFIG.extender_y,
      extender_width: width || DEFAULT_INPAINT_CONFIG.extender_x,
      // 允许客户端覆盖的配置
      ...(prompt !== undefined && { prompt }),
      ...(negative_prompt !== undefined && { negative_prompt }),
      ...(sd_steps !== undefined && { sd_steps }),
      ...(sd_guidance_scale !== undefined && { sd_guidance_scale }),
      ...otherConfig,
    }

    // 调用IOPaint API
    const response = await fetch(`${baseUrl}/api/v1/inpaint`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('IOPaint API error:', {
        status: response.status,
        statusText: response.statusText,
        error: errorText,
      })
      
      return NextResponse.json(
        { 
          error: `IOPaint API error: ${response.status} ${response.statusText}`,
          details: errorText 
        },
        { status: response.status }
      )
    }

    // IOPaint返回图片blob，我们需要转换为base64或直接返回
    const imageBlob = await response.blob()
    const arrayBuffer = await imageBlob.arrayBuffer()
    const buffer = Buffer.from(arrayBuffer)
    
    // 返回图片数据
    return new NextResponse(buffer, {
      status: 200,
      headers: {
        'Content-Type': imageBlob.type || 'image/png',
        'Cache-Control': 'no-cache',
      },
    })

  } catch (error) {
    console.error('Inpaint API error:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
