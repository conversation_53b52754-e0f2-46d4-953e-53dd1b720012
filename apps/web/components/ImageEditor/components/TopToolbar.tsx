'use client'

import React, { useState, useRef } from 'react'
import { But<PERSON> } from './ui/button'
import { Separator } from './ui/separator'
import { 
  Undo, 
  Redo, 
  Trash2, 
  UnfoldHorizontal, 
  Scissors, 
  <PERSON>lette, 
  Focus, 
  Wand2,
  Settings,
  ChevronDown,
  ZoomIn,
  ZoomOut,
  RotateCcw
} from 'lucide-react'
import { ZoomControls } from './ZoomControls'
import { BackgroundSelector } from './BackgroundSelector'
import { Slider } from './ui/slider'

interface TopToolbarProps {
  // History controls
  canUndo: boolean
  canRedo: boolean
  onUndo: () => void
  onRedo: () => void
  onClearAll: () => void

  // Zoom controls
  zoom: number
  onZoomChange: (zoom: number) => void
  onPanChange: (pan: { x: number; y: number }) => void

  // Comparison
  hasResults: boolean
  onCompareStart: () => void
  onCompareEnd: () => void
  compareTooltip: string

  // Background operations
  onRemoveBackground: () => void
  isBackgroundProcessing: boolean
  backgroundRemovedImageUrl?: string | null
  onReplaceBackground?: (backgroundUrl: string) => void
  originalImageUrl: string

  // Blur operations
  onBlurBackground?: (intensity: number) => void
  isBackgroundBlurProcessing: boolean
  backgroundBlurredImageUrl?: string | null
  blurIntensity: number
  onBlurIntensityChange: (intensity: number) => void

  // Object removal
  onProcessImage: () => void
  isProcessing: boolean

  // Global state
  disabled: boolean
}

export const TopToolbar: React.FC<TopToolbarProps> = ({
  canUndo,
  canRedo,
  onUndo,
  onRedo,
  onClearAll,
  zoom,
  onZoomChange,
  onPanChange,
  hasResults,
  onCompareStart,
  onCompareEnd,
  compareTooltip,
  onRemoveBackground,
  isBackgroundProcessing,
  backgroundRemovedImageUrl,
  onReplaceBackground,
  originalImageUrl,
  onBlurBackground,
  isBackgroundBlurProcessing,
  backgroundBlurredImageUrl,
  blurIntensity,
  onBlurIntensityChange,
  onProcessImage,
  isProcessing,
  disabled,
}) => {
  const [showBackgroundSelector, setShowBackgroundSelector] = useState(false)
  const [showBlurSelector, setShowBlurSelector] = useState(false)
  const backgroundSelectorRef = useRef<HTMLDivElement>(null)
  const blurSelectorRef = useRef<HTMLDivElement>(null)

  const isAnyProcessing = disabled || isProcessing || isBackgroundProcessing || isBackgroundBlurProcessing

  const handleColorSelect = (color: string) => {
    if (onReplaceBackground) {
      onReplaceBackground(color)
    }
    setShowBackgroundSelector(false)
  }

  const handleImageSelect = (imageUrl: string) => {
    if (onReplaceBackground) {
      onReplaceBackground(imageUrl)
    }
    setShowBackgroundSelector(false)
  }

  const handleBlurIntensityChange = (intensity: number) => {
    onBlurIntensityChange(intensity)
  }

  return (
    <div className="bg-white border-b border-gray-200 shadow-sm">
      <div className="flex items-center justify-between px-4 py-3">
        {/* Left Section - History & Canvas Controls */}
        <div className="flex items-center gap-1">
          {/* History Controls */}
          <div className="flex items-center gap-1 mr-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={onUndo}
              disabled={isAnyProcessing || !canUndo}
              className="h-8 w-8 p-0 hover:bg-gray-100"
              title="Undo (Ctrl+Z)"
            >
              <Undo className="w-4 h-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={onRedo}
              disabled={isAnyProcessing || !canRedo}
              className="h-8 w-8 p-0 hover:bg-gray-100"
              title="Redo (Ctrl+Y)"
            >
              <Redo className="w-4 h-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClearAll}
              disabled={isAnyProcessing}
              className="h-8 w-8 p-0 hover:bg-gray-100 text-red-600 hover:text-red-700"
              title="Clear All"
            >
              <Trash2 className="w-4 h-4" />
            </Button>
          </div>

          <Separator orientation="vertical" className="h-6 mx-2" />

          {/* Zoom Controls */}
          <ZoomControls
            zoom={zoom}
            onZoomChange={onZoomChange}
            onPanChange={onPanChange}
            disabled={isAnyProcessing}
          />

          {/* Compare Button */}
          {hasResults && (
            <>
              <Separator orientation="vertical" className="h-6 mx-2" />
              <Button
                variant="ghost"
                size="sm"
                onMouseDown={onCompareStart}
                onMouseUp={onCompareEnd}
                onMouseLeave={onCompareEnd}
                onTouchStart={onCompareStart}
                onTouchEnd={onCompareEnd}
                disabled={isAnyProcessing}
                className="h-8 px-3 hover:bg-gray-300"
                title={compareTooltip}
              >
                <UnfoldHorizontal className="w-4 h-4 mr-1" />
                <span className="text-xs">Compare</span>
              </Button>
            </>
          )}
        </div>

        {/* Right Section - AI Operations */}
        <div className="flex items-center gap-2">
          {/* Background Operations Group */}
          <div className="flex items-center gap-1 px-3 py-1 bg-purple-50 rounded-lg border border-purple-200">
            <Button
              onClick={onRemoveBackground}
              disabled={isAnyProcessing || !onRemoveBackground}
              size="sm"
              className="bg-purple-600 hover:bg-purple-700 text-white h-8"
            >
              <Scissors className="w-4 h-4 mr-1" />
              {isBackgroundProcessing ? 'Processing...' : 'Remove BG'}
            </Button>

            {/* Background Replacement */}
            {backgroundRemovedImageUrl && onReplaceBackground && (
              <div ref={backgroundSelectorRef} className="relative">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowBackgroundSelector(!showBackgroundSelector)}
                  disabled={isAnyProcessing}
                  className="h-8 border-purple-300 text-purple-700 hover:bg-purple-100"
                  title="Replace background"
                >
                  <Palette className="w-4 h-4 mr-1" />
                  <ChevronDown className="w-3 h-3" />
                </Button>

                <BackgroundSelector
                  isOpen={showBackgroundSelector}
                  onClose={() => setShowBackgroundSelector(false)}
                  onSelectColor={handleColorSelect}
                  onSelectImage={handleImageSelect}
                  originalImageUrl={originalImageUrl}
                  disabled={isAnyProcessing}
                />
              </div>
            )}

            {/* Background Blur */}
            <div ref={blurSelectorRef} className="relative">
              <Button
                onClick={() => {
                  if (onBlurBackground) {
                    onBlurBackground(blurIntensity)
                    if (!backgroundBlurredImageUrl) {
                      setTimeout(() => setShowBlurSelector(true), 1000)
                    }
                  }
                }}
                disabled={isAnyProcessing || !onBlurBackground}
                size="sm"
                className="bg-orange-600 hover:bg-orange-700 text-white h-8"
              >
                <Focus className="w-4 h-4 mr-1" />
                {isBackgroundBlurProcessing ? 'Processing...' : 'Blur BG'}
              </Button>

              {/* Blur Intensity Control */}
              {backgroundBlurredImageUrl && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowBlurSelector(!showBlurSelector)}
                  disabled={isAnyProcessing}
                  className="h-8 ml-1 border-orange-300 text-orange-700 hover:bg-orange-100"
                  title="Adjust blur intensity"
                >
                  <Settings className="w-4 h-4 mr-1" />
                  <ChevronDown className="w-3 h-3" />
                </Button>
              )}

              {/* Blur Selector Dropdown */}
              {showBlurSelector && backgroundBlurredImageUrl && (
                <div className="absolute top-full right-0 mt-2 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                  <div className="p-4">
                    <h4 className="text-sm font-medium text-gray-700 mb-3">
                      Blur Intensity
                    </h4>
                    <div className="space-y-3">
                      <Slider
                        value={[blurIntensity]}
                        onValueChange={(value) => handleBlurIntensityChange(value[0])}
                        max={100}
                        min={5}
                        step={5}
                        className="w-full"
                      />
                      <div className="flex justify-between text-xs text-gray-500">
                        <span>Light (5%)</span>
                        <span className="font-medium">{blurIntensity}%</span>
                        <span>Heavy (100%)</span>
                      </div>
                      <div className="flex gap-2 mt-3">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleBlurIntensityChange(20)}
                          className="flex-1 text-xs"
                        >
                          Default (20%)
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => {
                            if (onBlurBackground) {
                              onBlurBackground(blurIntensity)
                            }
                            setShowBlurSelector(false)
                          }}
                          disabled={!onBlurBackground}
                          className="flex-1 bg-orange-600 hover:bg-orange-700 text-white text-xs"
                        >
                          Apply Blur
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          <Separator orientation="vertical" className="h-8" />

          {/* Object Removal */}
          <Button
            onClick={onProcessImage}
            disabled={isAnyProcessing}
            className="bg-blue-600 hover:bg-blue-700 text-white h-9 px-4"
          >
            <Wand2 className="w-4 h-4 mr-2" />
            {isProcessing ? 'Processing...' : 'Remove Objects'}
          </Button>
        </div>
      </div>
    </div>
  )
}
