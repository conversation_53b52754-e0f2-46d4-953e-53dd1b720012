/**
 * 下载相关的工具函数
 */

export type FileType = 'png' | 'jpg' | 'jpeg' | 'webp'
export type QualityType = 'preview' | 'max' | 'original'

/**
 * 下载文件
 * @param url 文件URL或data URL
 * @param filename 文件名
 */
export function downloadFile(url: string, filename: string): void {
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

/**
 * 根据结果类型生成文件名
 * @param resultType 结果类型
 * @param quality 质量等级
 * @param fileType 文件类型
 * @returns 生成的文件名
 */
export function generateFileName(
  resultType: 'final' | 'inpaint' | 'background' | 'blur' | 'none',
  quality: QualityType = 'original',
  fileType: FileType = 'png'
): string {
  const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')
  const qualitySuffix = quality !== 'original' ? `-${quality}` : ''

  const typeMap = {
    final: 'final-result',
    inpaint: 'processed-image',
    background: 'background-removed',
    blur: 'background-blurred',
    none: 'image',
  }

  const baseName = typeMap[resultType] || 'image'
  return `${baseName}${qualitySuffix}-${timestamp}.${fileType}`
}

/**
 * 下载Canvas内容
 * @param canvas Canvas元素
 * @param filename 文件名
 * @param fileType 文件类型
 * @param quality 图片质量 (0-1)，仅对jpg/jpeg有效
 */
export function downloadCanvas(
  canvas: HTMLCanvasElement,
  filename: string,
  fileType: FileType = 'png',
  quality: number = 0.9
): void {
  const mimeType = `image/${fileType}`
  const dataURL = canvas.toDataURL(mimeType, quality)
  downloadFile(dataURL, filename)
}

/**
 * 下载图片URL
 * @param imageUrl 图片URL
 * @param filename 文件名
 */
export function downloadImageUrl(imageUrl: string, filename: string): void {
  downloadFile(imageUrl, filename)
}

/**
 * 将Blob转换为下载链接并下载
 * @param blob Blob对象
 * @param filename 文件名
 */
export function downloadBlob(blob: Blob, filename: string): void {
  const url = URL.createObjectURL(blob)
  downloadFile(url, filename)
  // 清理URL对象
  setTimeout(() => URL.revokeObjectURL(url), 100)
}

/**
 * 批量下载文件
 * @param downloads 下载项数组
 */
export interface DownloadItem {
  url: string
  filename: string
}

export function downloadMultipleFiles(
  downloads: DownloadItem[],
  delay: number = 100
): void {
  downloads.forEach((item, index) => {
    setTimeout(() => {
      downloadFile(item.url, item.filename)
    }, index * delay)
  })
}

/**
 * 创建ZIP文件并下载（需要JSZip库）
 * 注意：这个函数需要安装jszip依赖才能使用
 * 目前项目中未安装JSZip，所以暂时禁用此功能
 */
export async function downloadAsZip(
  files: Array<{ name: string; content: string | Blob }>,
  zipFilename: string
): Promise<void> {
  console.warn(
    'ZIP download is not available. JSZip dependency is not installed.'
  )

  // 回退到单独下载每个文件
  files.forEach((file, index) => {
    if (typeof file.content === 'string') {
      setTimeout(
        () => downloadFile(file.content as string, file.name),
        index * 100
      )
    }
  })
}

/**
 * 检查浏览器是否支持下载
 * @returns 是否支持下载
 */
export function isDownloadSupported(): boolean {
  const link = document.createElement('a')
  return typeof link.download !== 'undefined'
}

/**
 * 获取文件扩展名
 * @param filename 文件名
 * @returns 文件扩展名
 */
export function getFileExtension(filename: string): string {
  return filename.split('.').pop()?.toLowerCase() || ''
}

/**
 * 验证文件类型
 * @param filename 文件名
 * @param allowedTypes 允许的文件类型
 * @returns 是否为允许的文件类型
 */
export function isValidFileType(
  filename: string,
  allowedTypes: FileType[]
): boolean {
  const extension = getFileExtension(filename) as FileType
  return allowedTypes.includes(extension)
}

/**
 * 下载mask canvas
 * @param maskCanvas mask canvas元素
 * @param filename 文件名
 * @param maskColor 可选的mask颜色，如果提供则将mask转换为指定颜色
 */
export function downloadMask(
  maskCanvas: HTMLCanvasElement,
  filename?: string,
  maskColor?: string
): void {
  const defaultFilename = generateFileName('none', 'original', 'png').replace(
    'image',
    'mask'
  )

  if (maskColor) {
    // 创建一个新的canvas，将mask转换为指定颜色
    const coloredCanvas = createColoredMask(maskCanvas, maskColor)
    downloadCanvas(coloredCanvas, filename || defaultFilename, 'png')
  } else {
    // 直接下载原始mask
    downloadCanvas(maskCanvas, filename || defaultFilename, 'png')
  }
}

/**
 * 创建指定颜色的mask
 * @param maskCanvas 原始mask canvas
 * @param color 目标颜色
 * @returns 新的彩色mask canvas
 */
function createColoredMask(
  maskCanvas: HTMLCanvasElement,
  color: string
): HTMLCanvasElement {
  const coloredCanvas = document.createElement('canvas')
  coloredCanvas.width = maskCanvas.width
  coloredCanvas.height = maskCanvas.height
  const coloredCtx = coloredCanvas.getContext('2d')

  if (!coloredCtx) {
    throw new Error('Failed to get colored canvas context')
  }

  const maskCtx = maskCanvas.getContext('2d')
  if (!maskCtx) {
    throw new Error('Failed to get mask canvas context')
  }

  const maskImageData = maskCtx.getImageData(
    0,
    0,
    maskCanvas.width,
    maskCanvas.height
  )
  const coloredImageData = coloredCtx.createImageData(
    maskCanvas.width,
    maskCanvas.height
  )

  // 解析颜色
  const rgbColor = hexToRgb(color) || { r: 255, g: 0, b: 0 }

  // 将mask转换为指定颜色
  for (let i = 0; i < maskImageData.data.length; i += 4) {
    const alpha = maskImageData.data[i + 3]
    if (alpha > 0) {
      coloredImageData.data[i] = rgbColor.r // R
      coloredImageData.data[i + 1] = rgbColor.g // G
      coloredImageData.data[i + 2] = rgbColor.b // B
      coloredImageData.data[i + 3] = alpha // A
    } else {
      coloredImageData.data[i] = 0
      coloredImageData.data[i + 1] = 0
      coloredImageData.data[i + 2] = 0
      coloredImageData.data[i + 3] = 0
    }
  }

  coloredCtx.putImageData(coloredImageData, 0, 0)
  return coloredCanvas
}

/**
 * 创建图片和mask的合成图
 * @param imageUrl 原图片URL
 * @param maskCanvas mask canvas
 * @param maskColor mask颜色 (默认红色)
 * @param maskOpacity mask透明度 (默认0.5)
 * @returns 合成图的data URL
 */
export async function createImageMaskComposite(
  imageUrl: string,
  maskCanvas: HTMLCanvasElement,
  maskColor: string = '#ff0000',
  maskOpacity: number = 0.5
): Promise<string> {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.crossOrigin = 'anonymous'

    img.onload = () => {
      // 创建合成canvas
      const compositeCanvas = document.createElement('canvas')
      compositeCanvas.width = img.width
      compositeCanvas.height = img.height
      const ctx = compositeCanvas.getContext('2d')

      if (!ctx) {
        reject(new Error('Failed to get canvas context'))
        return
      }

      // 绘制原图
      ctx.drawImage(img, 0, 0)

      // 创建mask的彩色版本
      const coloredMaskCanvas = document.createElement('canvas')
      coloredMaskCanvas.width = maskCanvas.width
      coloredMaskCanvas.height = maskCanvas.height
      const coloredMaskCtx = coloredMaskCanvas.getContext('2d')

      if (!coloredMaskCtx) {
        reject(new Error('Failed to get colored mask canvas context'))
        return
      }

      // 获取mask数据
      const maskCtx = maskCanvas.getContext('2d')
      if (!maskCtx) {
        reject(new Error('Failed to get mask canvas context'))
        return
      }

      const maskImageData = maskCtx.getImageData(
        0,
        0,
        maskCanvas.width,
        maskCanvas.height
      )
      const coloredMaskImageData = coloredMaskCtx.createImageData(
        maskCanvas.width,
        maskCanvas.height
      )

      // 解析mask颜色
      const color = hexToRgb(maskColor) || { r: 255, g: 0, b: 0 }

      // 将mask转换为指定颜色
      for (let i = 0; i < maskImageData.data.length; i += 4) {
        const alpha = maskImageData.data[i + 3]
        if (alpha > 0) {
          coloredMaskImageData.data[i] = color.r // R
          coloredMaskImageData.data[i + 1] = color.g // G
          coloredMaskImageData.data[i + 2] = color.b // B
          coloredMaskImageData.data[i + 3] = alpha // A
        } else {
          coloredMaskImageData.data[i] = 0
          coloredMaskImageData.data[i + 1] = 0
          coloredMaskImageData.data[i + 2] = 0
          coloredMaskImageData.data[i + 3] = 0
        }
      }

      coloredMaskCtx.putImageData(coloredMaskImageData, 0, 0)

      // 将彩色mask绘制到合成canvas上
      ctx.globalAlpha = maskOpacity
      ctx.drawImage(
        coloredMaskCanvas,
        0,
        0,
        maskCanvas.width,
        maskCanvas.height,
        0,
        0,
        img.width,
        img.height
      )

      // 恢复透明度
      ctx.globalAlpha = 1.0

      resolve(compositeCanvas.toDataURL('image/png'))
    }

    img.onerror = () => reject(new Error('Failed to load image'))
    img.src = imageUrl
  })
}

/**
 * 下载图片和mask的合成图
 * @param imageUrl 原图片URL
 * @param maskCanvas mask canvas
 * @param filename 文件名
 * @param maskColor mask颜色
 * @param maskOpacity mask透明度
 */
export async function downloadImageMaskComposite(
  imageUrl: string,
  maskCanvas: HTMLCanvasElement,
  filename?: string,
  maskColor?: string,
  maskOpacity?: number
): Promise<void> {
  try {
    const compositeDataUrl = await createImageMaskComposite(
      imageUrl,
      maskCanvas,
      maskColor,
      maskOpacity
    )
    const defaultFilename = generateFileName('none', 'original', 'png').replace(
      'image',
      'composite'
    )
    downloadFile(compositeDataUrl, filename || defaultFilename)
  } catch (error) {
    console.error('Failed to download composite image:', error)
    throw error
  }
}

/**
 * 将hex颜色转换为RGB
 * @param hex hex颜色值
 * @returns RGB对象
 */
function hexToRgb(hex: string): { r: number; g: number; b: number } | null {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
  return result
    ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16),
      }
    : null
}
