import React from 'react'

const UploadProgress = ({
  simulatedProgress,
}: {
  simulatedProgress: number
}) => {
  return (
    <div className="flex pt-[72px] h-screen items-center justify-center">
      <div className="max-w-md w-full p-8">
        <div className="text-center space-y-6">
          <div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto">
            <div className="text-white font-bold">{simulatedProgress}%</div>
          </div>

          {/* Progress Bar */}
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div
              className="bg-gradient-to-r from-blue-400 to-blue-600 h-3 rounded-full transition-all duration-300"
              style={{ width: `${simulatedProgress || 0}%` }}
            />
          </div>

          <div>
            <h3 className="text-xl font-semibold text-gray-800 mb-2">
              Processing Image...
            </h3>
            <p className="text-gray-600">
              Please wait while we prepare your image for editing
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default UploadProgress
